# Model Updates Summary

## Changes Made

### 1. ApplicantsModel.php Updates

#### New Field Added:
- **`employee_of_org_id`** - INT(11) UNSIGNED NULL DEFAULT NULL
  - Added to `$allowedFields` array
  - Positioned after `public_service_file_number` field
  - This field will appear if the employee is a public servant
  - References the `dakoii_org` table for organization selection

#### New Methods Added:

1. **`getApplicantWithOrganization($id)`**
   - Retrieves applicant data with organization details
   - Uses LEFT JOIN with `dakoii_org` table
   - Returns applicant data with organization name

2. **`getOrganizationsForDropdown()`**
   - Returns array of active organizations for dropdown selection
   - Format: `[id => org_name]`
   - Includes "Not Listed" as default option with empty value
   - Orders organizations alphabetically by name

### 2. EducationLevelsModel.php Updates

#### New Field Added:
- **`priority`** - INT UNSIGNED NOT NULL DEFAULT 0
  - Added to `$allowedFields` array
  - Positioned after `remarks` field
  - Used for ordering education levels by priority

#### Updated Methods:

1. **`getActiveEducationLevels()`**
   - Now orders by priority first, then by name
   - Ensures consistent ordering based on priority

#### New Methods Added:

1. **`getEducationLevelsByPriority()`**
   - Returns education levels ordered by priority
   - Secondary ordering by name

2. **`updatePriority($id, $priority)`**
   - Updates the priority of a specific education level
   - Simple method for priority management

#### Updated Validation:
- Added validation rules for `priority` field:
  - `permit_empty|integer|greater_than_equal_to[0]`
- Added validation messages for priority field

## Database Changes Required

Run the SQL commands in `database_updates.sql` to:

1. **Add `employee_of_org_id` column to `applicants` table**
   - Includes foreign key constraint to `dakoii_org` table
   - ON DELETE SET NULL, ON UPDATE CASCADE

2. **Add `priority` column to `education_levels` table**
   - Includes index for better performance
   - Default value of 0 for existing records

## Usage Notes

### For Applicant Organization Selection:
- The dropdown will show all active organizations from `dakoii_org` table
- If organization is not listed, users can leave it empty
- "Not Listed" option is provided as default
- Field only appears when `is_public_servant` is checked

### For Education Level Priority:
- Lower priority numbers appear first in listings
- Priority 0 is default for new education levels
- Can be used to order education levels logically (e.g., Primary=1, Secondary=2, etc.)
- Maintains alphabetical ordering as secondary sort

## Implementation Requirements

1. **Database Updates**: Run the SQL commands in `database_updates.sql`
2. **Form Updates**: Update applicant profile forms to include organization dropdown
3. **View Updates**: Update education level management views to include priority field
4. **Controller Updates**: Update controllers to handle the new fields

## Benefits

1. **Better Organization Management**: Public servants can now be linked to their organizations
2. **Improved Education Level Ordering**: Priority-based ordering for better user experience
3. **Flexible Organization Selection**: "Not Listed" option for organizations not in the system
4. **Maintainable Code**: Clean model methods for easy integration
