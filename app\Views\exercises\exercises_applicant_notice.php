<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('dashboard') ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('exercises') ?>">Exercise Management</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Applicant Notice
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-bell me-2"></i>Applicant Notice
                    </h5>
                    <small class="text-muted">Exercise: <?= esc($exercise['exercise_name']) ?></small>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('exercises') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Exercise Management
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?= form_open('exercises/applicant_notice/' . $exercise['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>
                <div class="row">
                    <div class="col-12">
                        <label class="form-label">Applicant Notice <span class="text-danger">*</span></label>
                        <p class="text-muted small">Write a notice or important information for applicants regarding this exercise.</p>
                        <textarea name="applicant_notice" class="form-control" rows="12" placeholder="Enter notice for applicants..." required><?= esc($exercise['pre_screen_criteria'] ?? '') ?></textarea>
                        <div class="invalid-feedback">
                            Please provide an applicant notice.
                        </div>
                        <div class="form-text">
                            <small class="text-muted">
                                <i class="fas fa-info-circle me-1"></i>
                                This notice will be displayed to applicants. Keep it clear and informative.
                            </small>
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Applicant Notice
                        </button>
                        <a href="<?= base_url('exercises') ?>" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });

    // Auto-resize textarea
    const textarea = document.querySelector('textarea[name="applicant_notice"]');
    if (textarea) {
        textarea.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight) + 'px';
        });
        
        // Initial resize
        textarea.style.height = 'auto';
        textarea.style.height = (textarea.scrollHeight) + 'px';
    }
});
</script>
<?= $this->endSection() ?>
