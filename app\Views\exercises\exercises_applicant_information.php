<?= $this->extend('templates/nolstemp') ?>

<?= $this->section('content') ?>
<div class="container-fluid">
    <!-- Breadcrumb Navigation -->
    <div class="row mb-3">
        <div class="col-12">
            <nav aria-label="breadcrumb">
                <ol class="breadcrumb">
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('dashboard') ?>">Dashboard</a>
                    </li>
                    <li class="breadcrumb-item">
                        <a href="<?= base_url('exercises') ?>">Exercise Management</a>
                    </li>
                    <li class="breadcrumb-item active" aria-current="page">
                        Applicant Information
                    </li>
                </ol>
            </nav>
        </div>
    </div>

    <div class="card">
        <div class="card-header bg-white py-3">
            <div class="row align-items-center">
                <div class="col">
                    <h5 class="mb-0">
                        <i class="fas fa-info-circle me-2"></i>Applicant Information
                    </h5>
                    <small class="text-muted">Exercise: <?= esc($exercise['exercise_name']) ?></small>
                </div>
                <div class="col-auto">
                    <a href="<?= base_url('exercises') ?>" class="btn btn-outline-secondary">
                        <i class="fas fa-arrow-left me-2"></i>Back to Exercise Management
                    </a>
                </div>
            </div>
        </div>
        <div class="card-body">
            <?php if (session()->getFlashdata('success')): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('success') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?php if (session()->getFlashdata('error')): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <?= session()->getFlashdata('error') ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                </div>
            <?php endif; ?>

            <?= form_open('exercises/applicant_information/' . $exercise['id'], ['class' => 'needs-validation', 'novalidate' => true]) ?>
                <div class="row">
                    <div class="col-12">
                        <label class="form-label">Applicant Information <span class="text-danger">*</span></label>
                        <p class="text-muted small">Use the rich text editor below to format your applicant information. You can use bold, italic, underline, and other formatting options.</p>

                        <!-- CKEditor 5 -->
                        <div id="editor" style="min-height: 300px; border: 1px solid #ced4da; border-radius: 0.375rem;"></div>
                        <textarea id="applicant_information" name="applicant_information" class="form-control d-none" required><?= esc($exercise['applicants_information'] ?? '') ?></textarea>
                        <div class="invalid-feedback">
                            Please provide applicant information.
                        </div>
                    </div>
                </div>

                <div class="row mt-4">
                    <div class="col-12">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-save me-2"></i>Save Applicant Information
                        </button>
                        <a href="<?= base_url('exercises') ?>" class="btn btn-secondary ms-2">
                            <i class="fas fa-times me-2"></i>Cancel
                        </a>
                    </div>
                </div>
            <?= form_close() ?>
        </div>
    </div>
</div>

<!-- Include CKEditor 5 for rich text editing -->
<script src="https://cdn.ckeditor.com/ckeditor5/39.0.1/classic/ckeditor.js"></script>

<script>
document.addEventListener('DOMContentLoaded', function() {
    let editor;

    // Initialize CKEditor 5
    ClassicEditor
        .create(document.querySelector('#editor'), {
            toolbar: {
                items: [
                    'heading',
                    '|',
                    'bold',
                    'italic',
                    'underline',
                    '|',
                    'bulletedList',
                    'numberedList',
                    '|',
                    'outdent',
                    'indent',
                    '|',
                    'link',
                    'blockQuote',
                    '|',
                    'undo',
                    'redo'
                ]
            },
            heading: {
                options: [
                    { model: 'paragraph', title: 'Paragraph', class: 'ck-heading_paragraph' },
                    { model: 'heading1', view: 'h1', title: 'Heading 1', class: 'ck-heading_heading1' },
                    { model: 'heading2', view: 'h2', title: 'Heading 2', class: 'ck-heading_heading2' },
                    { model: 'heading3', view: 'h3', title: 'Heading 3', class: 'ck-heading_heading3' }
                ]
            },
            placeholder: 'Enter applicant information here...'
        })
        .then(newEditor => {
            editor = newEditor;

            // Set initial content from textarea
            const initialContent = document.getElementById('applicant_information').value;
            if (initialContent) {
                editor.setData(initialContent);
            }
        })
        .catch(error => {
            console.error('Error initializing CKEditor:', error);
        });

    // Form validation
    const form = document.querySelector('.needs-validation');
    form.addEventListener('submit', function(event) {
        if (editor) {
            // Get content from CKEditor
            const editorContent = editor.getData();

            // Update hidden textarea with CKEditor content
            document.getElementById('applicant_information').value = editorContent;

            // Check if CKEditor has content (not just empty tags)
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = editorContent;
            const textContent = tempDiv.textContent || tempDiv.innerText || '';

            if (textContent.trim().length === 0) {
                event.preventDefault();
                event.stopPropagation();

                // Show custom validation message
                const editorContainer = document.querySelector('#editor');
                editorContainer.style.borderColor = '#dc3545';

                // Show error message
                const invalidFeedback = document.querySelector('.invalid-feedback');
                invalidFeedback.style.display = 'block';

                return false;
            } else {
                // Reset validation styling
                const editorContainer = document.querySelector('#editor');
                editorContainer.style.borderColor = '#198754';

                // Hide error message
                const invalidFeedback = document.querySelector('.invalid-feedback');
                invalidFeedback.style.display = 'none';
            }
        }

        if (!form.checkValidity()) {
            event.preventDefault();
            event.stopPropagation();
        }
        form.classList.add('was-validated');
    });
});
</script>
<?= $this->endSection() ?>
