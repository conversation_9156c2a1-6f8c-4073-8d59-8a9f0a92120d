<?php

namespace App\Controllers;

class ApplicantApplicationController extends BaseController
{
    protected $session;

    public function __construct()
    {
        helper(['form', 'url', 'application']);
        $this->session = session();
    }

    public function uploadFile()
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');
        $file = $this->request->getFile('file');
        $title = $this->request->getPost('file_title');
        $description = $this->request->getPost('file_description');

        if (!$file || !$file->isValid() || $file->hasMoved()) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Invalid file upload'
            ]);
        }

        // Create upload directory if it doesn't exist
        $uploadPath = FCPATH . 'uploads/applicant_files';
        if (!is_dir($uploadPath)) {
            mkdir($uploadPath, 0777, true);
        }

        try {
            // Generate unique filename
            $newName = $applicant_id . '_' . time() . '_' . $file->getRandomName();

            // Move file to uploads directory
            if ($file->move($uploadPath, $newName)) {
                // Save file information to database
                $fileData = [
                    'applicant_id' => $applicant_id,
                    'title' => $title,
                    'description' => $description,
                    'file_path' => 'public/uploads/applicant_files/' . $newName,
                    'file_name' => $file->getName(),
                    'file_size' => $file->getSize(),
                    'file_type' => $file->getMimeType(),
                    'created_by' => $applicant_id,
                    'updated_by' => $applicant_id
                ];

                // Simulate successful file save for UI development
                // Replace with actual model insert call later

                return $this->response->setJSON([
                    'success' => true,
                    'message' => 'File uploaded successfully',
                    'file' => $fileData
                ]);
            } else {
                return $this->response->setJSON([
                    'success' => false,
                    'message' => 'Error moving uploaded file'
                ]);
            }
        } catch (\Exception $e) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'Error uploading file: ' . $e->getMessage()
            ]);
        }
    }

    public function deleteFile($id)
    {
        if (!$this->request->isAJAX()) {
            return $this->response->setJSON(['success' => false, 'message' => 'Invalid request']);
        }

        $applicant_id = session()->get('applicant_id');

        // For UI development - simulate ownership verification
        if (!$id) {
            return $this->response->setJSON([
                'success' => false,
                'message' => 'File not found or access denied'
            ]);
        }

        // For UI development - simulate successful file deletion
        return $this->response->setJSON([
            'success' => true,
            'message' => 'File deleted successfully'
        ]);
    }

    public function viewApplication($applicationId)
    {
        try {
            // Get current applicant ID from session
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to continue');
            }

            // Load models
            $appxApplicationModel = new \App\Models\AppxApplicationDetailsModel();
            $appxEducationModel = new \App\Models\AppxApplicationEducationModel();
            $appxExperienceModel = new \App\Models\AppxApplicationExperiencesModel();
            $appxFilesModel = new \App\Models\AppxApplicationFilesModel();
            $positionsModel = new \App\Models\PositionsModel();
            $exerciseModel = new \App\Models\ExerciseModel();
            $orgModel = new \App\Models\DakoiiOrgModel();
            $educationLevelsModel = new \App\Models\EducationLevelsModel();

            // Get application details
            $application = $appxApplicationModel
                ->where('id', $applicationId)
                ->where('applicant_id', $applicant_id)
                ->first();

            if (!$application) {
                log_message('error', "Application not found: ID=$applicationId, Applicant=$applicant_id");
                return redirect()->to('/applicant/applications')->with('error', 'Application not found or access denied.');
            }

            log_message('info', 'Application found: ' . json_encode($application));

            // Get position details
            $position = $positionsModel->find($application['position_id']);
            if (!$position) {
                return redirect()->to('/applicant/applications')->with('error', 'Position information not found.');
            }

            // Get exercise details
            $exercise = $exerciseModel->find($application['exercise_id']);

            // Get organization details
            $organization = $orgModel->find($application['org_id']);

            // Get education records
            $education = $appxEducationModel
                ->where('application_id', $applicationId)
                ->orderBy('date_from', 'DESC')
                ->findAll();

            // Get experience records
            $experiences = $appxExperienceModel
                ->where('application_id', $applicationId)
                ->orderBy('date_from', 'DESC')
                ->findAll();

            // Get file records
            $files = $appxFilesModel
                ->where('application_id', $applicationId)
                ->findAll();

            // Get education levels for display
            $educationLevelsData = $educationLevelsModel->orderBy('priority', 'ASC')->findAll();
            $educationLevels = [];
            foreach ($educationLevelsData as $level) {
                $educationLevels[$level['id']] = $level['name'];
            }

            // Calculate total experience
            $totalExperience = 0;
            foreach ($experiences as $exp) {
                if ($exp['date_from'] && $exp['date_to']) {
                    $from = new \DateTime($exp['date_from']);
                    $to = new \DateTime($exp['date_to']);
                    $diff = $from->diff($to);
                    $totalExperience += $diff->y + ($diff->m / 12);
                }
            }

            // Get highest education
            $highestEducation = 'Not specified';
            if (!empty($education)) {
                $highestLevel = 0;
                foreach ($education as $edu) {
                    $currentLevel = $edu['education_level'] ?? 0;
                    if ($currentLevel > $highestLevel) {
                        $highestLevel = $currentLevel;
                        $highestEducation = ($edu['course'] ?? 'Unknown Course') . ' - ' . ($edu['institution'] ?? 'Unknown Institution');
                    }
                }
            }

            // Parse profile details if available
            $profileDetails = null;
            if (!empty($application['profile_details'])) {
                $profileDetails = json_decode($application['profile_details'], true);
            }

            // Prepare data for the view
            $data = [
                'title' => 'Application Details - ' . $application['application_number'],
                'menu' => 'applications',
                'application' => $application,
                'position' => $position,
                'organization' => $organization,
                'exercise' => $exercise,
                'files' => $files,
                'experiences' => $experiences,
                'education' => $education,
                'totalExperience' => round($totalExperience, 1),
                'highestEducation' => $highestEducation,
                'educationLevels' => $educationLevels,
                'profileDetails' => $profileDetails
            ];

            // Render the view
            return view('applicant/applicant_application_view', $data);

        } catch (\Exception $e) {
            log_message('error', 'View application error: ' . $e->getMessage());
            log_message('error', 'Stack trace: ' . $e->getTraceAsString());
            return redirect()->to('/applicant/applications')->with('error', 'Error loading application details: ' . $e->getMessage());
        }
    }

    public function applications()
    {
        $applicant_id = session()->get('applicant_id');

        // Using dummy data for UI development
        $applications = [
            [
                'id' => 1,
                'position_title' => 'Software Developer',
                'organization' => 'Tech Solutions Inc.',
                'applied_date' => '2024-01-15',
                'status' => 'pending'
            ],
            [
                'id' => 2,
                'position_title' => 'Project Manager',
                'organization' => 'Business Corp',
                'applied_date' => '2024-01-10',
                'status' => 'shortlisted'
            ]
        ];

        $data = [
            'title' => 'My Applications',
            'menu' => 'applications',
            'applications' => $applications
        ];

        return view('applicant/applicant_applications', $data);
    }

    /**
     * Edit files interface for an application
     */
    public function editFiles($applicationId)
    {
        try {
            // Get current applicant ID from session
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to continue');
            }

            // Load models
            $appxApplicationModel = new \App\Models\AppxApplicationDetailsModel();
            $appxFilesModel = new \App\Models\AppxApplicationFilesModel();
            $exerciseModel = new \App\Models\ExerciseModel();
            $positionsModel = new \App\Models\PositionsModel();
            $orgModel = new \App\Models\DakoiiOrgModel();

            // Get application details
            $application = $appxApplicationModel
                ->where('id', $applicationId)
                ->where('applicant_id', $applicant_id)
                ->first();

            if (!$application) {
                return redirect()->to('/applicant/applications')->with('error', 'Application not found or access denied.');
            }

            // Get exercise details to check status
            $exercise = $exerciseModel->find($application['exercise_id']);

            if (!$exercise || $exercise['status'] !== 'published') {
                return redirect()->to('/applicant/application/' . $applicationId)
                    ->with('error', 'File editing is only available for published exercises.');
            }

            // Get position and organization details for display
            $position = $positionsModel->find($application['position_id']);
            $organization = $orgModel->find($application['org_id']);

            // Get current files
            $files = $appxFilesModel
                ->where('application_id', $applicationId)
                ->findAll();

            $data = [
                'title' => 'Edit Application Files - ' . $application['application_number'],
                'menu' => 'applications',
                'application' => $application,
                'position' => $position,
                'organization' => $organization,
                'exercise' => $exercise,
                'files' => $files
            ];

            return view('applicant/applicant_application_files_edit', $data);

        } catch (\Exception $e) {
            log_message('error', 'Edit files error: ' . $e->getMessage());
            return redirect()->to('/applicant/applications')->with('error', 'Error loading file management interface.');
        }
    }

    /**
     * Upload new file for an application
     */
    public function uploadApplicationFile($applicationId)
    {
        try {
            // Get current applicant ID from session
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to continue');
            }

            // Load models
            $appxApplicationModel = new \App\Models\AppxApplicationDetailsModel();
            $appxFilesModel = new \App\Models\AppxApplicationFilesModel();
            $exerciseModel = new \App\Models\ExerciseModel();

            // Verify application ownership and exercise status
            $application = $appxApplicationModel
                ->where('id', $applicationId)
                ->where('applicant_id', $applicant_id)
                ->first();

            if (!$application) {
                return redirect()->to('/applicant/applications')->with('error', 'Application not found or access denied.');
            }

            $exercise = $exerciseModel->find($application['exercise_id']);
            if (!$exercise || $exercise['status'] !== 'published') {
                return redirect()->to('/applicant/application/' . $applicationId)
                    ->with('error', 'File uploading is only available for published exercises.');
            }

            // Validate form input
            $validation = \Config\Services::validation();
            $validation->setRules([
                'file_title' => 'required|max_length[255]',
                'file_description' => 'permit_empty|max_length[500]',
                'file' => 'uploaded[file]|max_size[file,25600]|ext_in[file,pdf]'
            ]);

            if (!$validation->withRequest($this->request)->run()) {
                return redirect()->back()->withInput()->with('errors', $validation->getErrors());
            }

            // Handle file upload
            $file = $this->request->getFile('file');
            if ($file && $file->isValid() && !$file->hasMoved()) {
                $newName = $file->getRandomName();
                $uploadPath = FCPATH . 'uploads/applications/';

                // Create directory if it doesn't exist
                if (!is_dir($uploadPath)) {
                    mkdir($uploadPath, 0755, true);
                }

                if ($file->move($uploadPath, $newName)) {
                    // Save file record to database
                    $fileData = [
                        'application_id' => $applicationId,
                        'applicant_id' => $applicant_id,
                        'file_title' => $this->request->getPost('file_title'),
                        'file_description' => $this->request->getPost('file_description'),
                        'file_path' => 'public/uploads/applications/' . $newName,
                        'created_by' => $applicant_id
                    ];

                    if ($appxFilesModel->insert($fileData)) {
                        return redirect()->to('/applicant/application/' . $applicationId . '/files/edit')
                            ->with('success', 'File uploaded successfully.');
                    } else {
                        // Delete uploaded file if database insert failed
                        unlink($uploadPath . $newName);
                        return redirect()->back()->with('error', 'Failed to save file information.');
                    }
                } else {
                    return redirect()->back()->with('error', 'Failed to upload file.');
                }
            } else {
                return redirect()->back()->with('error', 'Invalid file or file upload error.');
            }

        } catch (\Exception $e) {
            log_message('error', 'Upload file error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error uploading file: ' . $e->getMessage());
        }
    }

    /**
     * Delete a file from an application
     */
    public function deleteApplicationFile($applicationId, $fileId)
    {
        try {
            // Get current applicant ID from session
            $applicant_id = session()->get('applicant_id');

            if (!$applicant_id) {
                return redirect()->to('applicant/login')->with('error', 'Please login to continue');
            }

            // Load models
            $appxApplicationModel = new \App\Models\AppxApplicationDetailsModel();
            $appxFilesModel = new \App\Models\AppxApplicationFilesModel();
            $exerciseModel = new \App\Models\ExerciseModel();

            // Verify application ownership and exercise status
            $application = $appxApplicationModel
                ->where('id', $applicationId)
                ->where('applicant_id', $applicant_id)
                ->first();

            if (!$application) {
                return redirect()->to('/applicant/applications')->with('error', 'Application not found or access denied.');
            }

            $exercise = $exerciseModel->find($application['exercise_id']);
            if (!$exercise || $exercise['status'] !== 'published') {
                return redirect()->to('/applicant/application/' . $applicationId)
                    ->with('error', 'File deletion is only available for published exercises.');
            }

            // Get file details and verify ownership
            $file = $appxFilesModel
                ->where('id', $fileId)
                ->where('application_id', $applicationId)
                ->where('applicant_id', $applicant_id)
                ->first();

            if (!$file) {
                return redirect()->back()->with('error', 'File not found or access denied.');
            }

            // Delete physical file (remove 'public/' prefix for file system path)
            $physicalPath = str_replace('public/', FCPATH, $file['file_path']);
            if (file_exists($physicalPath)) {
                unlink($physicalPath);
            }

            // Delete file record from database
            if ($appxFilesModel->delete($fileId)) {
                return redirect()->to('/applicant/application/' . $applicationId . '/files/edit')
                    ->with('success', 'File deleted successfully.');
            } else {
                return redirect()->back()->with('error', 'Failed to delete file record.');
            }

        } catch (\Exception $e) {
            log_message('error', 'Delete file error: ' . $e->getMessage());
            return redirect()->back()->with('error', 'Error deleting file: ' . $e->getMessage());
        }
    }
}
